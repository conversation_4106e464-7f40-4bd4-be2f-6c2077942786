{"EnterBI": "EnterBI", "Analytic": "<PERSON><PERSON><PERSON>", "Data Center": "<PERSON><PERSON>", "Data Studio": "<PERSON><PERSON>", "Objects": "<PERSON><PERSON><PERSON><PERSON>", "{{count}} record(s) found.": "{{count}} kayıt bulundu.", "Custom Tables": "<PERSON><PERSON>", "You are not allowed to delete this custom table!": "Bu özel tabloyu silme izniniz yok!", "Data export operation was started.": "Veri dışa aktarma işlemi başlatıldı.", "Data export operation was completed successfully.": "Veri dışa aktarma işlemi başarıyla tamamlandı.", "File download operation was started.": "Dosya indirme işlemi başlatıldı.", "This operation will remove and re-create all the records. Do you want continue?": "Bu işem tüm verileri silecek ve yeniden oluşturacaktır. <PERSON>am etmek istiyor musunuz?", "Can the objects": "Nesneleri görebilir", "Can edit the custom tables": "Özel tabloları güncelleyebilir", "Type value": "<PERSON><PERSON><PERSON>", "Document type value": "Belge türü <PERSON>", "Partner email": "İş ortağı e-posta", "Partner phone": "İş ortağı telefon", "Real special quantity": "Gerçek özel miktar", "Profit margin": "<PERSON><PERSON><PERSON> marjı", "Delivery address name": "Teslimat adresi adı", "Delivery address address": "Teslimat adresi", "Delivery address city": "Teslimat adresi <PERSON>", "Delivery address district": "Teslimat adresi ilçesi", "Delivery address sub district": "Teslimat adresi mahallesi", "Delivery address country": "Teslimat adresi <PERSON>", "Delivery address state": "Teslimat adresi eyaleti", "Delivery address street": "Teslimat adresi sokağı", "Delivery address door number": "Teslimat adresi kapı numarası", "Delivery address apartment number": "Teslimat adresi daire numarası", "Delivery address postal code": "Teslimat adresi posta kodu", "Invoice address code": "<PERSON>ura adresi kodu", "Invoice address name": "<PERSON>ura adresi adı", "Invoice address address": "<PERSON><PERSON> ad<PERSON>i", "Invoice address city": "<PERSON><PERSON> ad<PERSON>", "Invoice address district": "Fatura adresi il<PERSON>", "Invoice address sub district": "<PERSON>ura adresi mahallesi", "Invoice address country": "<PERSON>ura adresi <PERSON>", "Invoice address state": "Fatura adresi eyaleti", "Invoice address street": "Fatura adresi <PERSON>", "Invoice address door number": "Fatura adresi kapı numarası", "Invoice address apartment number": "Fatura adresi daire numarası", "Invoice address postal code": "<PERSON>ura adresi posta kodu", "Sub total": "<PERSON>", "Sub total after discount": "İndirim sonrası ara toplam", "Sub total SPB": "Ara toplam SPB", "Sub total after discount SPB": "İndirim sonrası ara toplam SPB", "Requested qty": "<PERSON><PERSON> edilen miktar", "Acutal qty": "Gerçekleşen miktar", "Customer email": "Müşteri e-postası", "Customer phone": "Müşteri telefonu", "Estimated closing interval": "<PERSON><PERSON><PERSON> aralığı", "Products amount": "Ürü<PERSON><PERSON>in tutarı", "Phone country code": "Telefon ülke kodu", "Sub district": "Mahalle", "Sector": "Se<PERSON><PERSON><PERSON>", "Related Documents": "Bağlantılı Belgeler", "Bom code": "<PERSON><PERSON> kodu", "Bom name": "Bo<PERSON> adı", "Item code": "<PERSON><PERSON><PERSON><PERSON> kodu", "Item definition": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı", "Item category": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Item category path": "<PERSON><PERSON><PERSON><PERSON> kategori yolu", "Item brand": "<PERSON><PERSON><PERSON><PERSON> mark<PERSON>", "Item barcode": "<PERSON><PERSON><PERSON><PERSON>", "Wastage type value": "Fire türü <PERSON>", "Product unit": "<PERSON><PERSON><PERSON><PERSON>", "Category path": "<PERSON><PERSON><PERSON> yolu", "Is e commerce product": "E-ticaret ü<PERSON>ü<PERSON>ü mü?", "Is simple": "Basit mi?", "Is variant": "Varyant mı?", "Is configurable": "Konfigüre edilebilir mi?", "Is kit": "Kit mi?", "Has alternate": "Alternatif var mı?", "Hs code": "Hs kodu", "Sales additional taxes": "Satış ek vergileri", "Price is determined by sub products": "Fiyat alt ürünler tarafından belirlenir", "Ecommerce seo title": "E-ticaret seo başlığı", "Ecommerce seo description": "E-ticaret seo a<PERSON><PERSON>kla<PERSON>ı", "Ecommerce delivery options": "E-ticaret teslimat seçenekleri", "Ecommerce dropshipping": "E-ticaret dropshipping", "Ecommerce same day delivery": "E-ticaret aynı gün teslimat", "Ecommerce estimated delivery duration": "<PERSON>-t<PERSON><PERSON> tahmini te<PERSON> süresi", "Ecommerce delivery at specified date": "E-ticaret belirtilen tarihte teslimat", "Ecommerce delivery at specified time": "<PERSON>-ticaret belirtilen saatte teslimat", "Ecommerce daily storage cost": "<PERSON><PERSON>t<PERSON><PERSON> gü<PERSON><PERSON><PERSON><PERSON> depol<PERSON> ma<PERSON>ti", "Purchase additional taxes": "Satın alma ek vergileri", "Tracking value": "<PERSON><PERSON><PERSON>", "Putaway strategy value": "<PERSON><PERSON><PERSON> strate<PERSON>", "Removal strategy value": "<PERSON><PERSON><PERSON><PERSON> strate<PERSON>", "Last cost": "Son maliyet", "Valuation method value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fixture asset type": "Demirbaş varlık türü", "Fixture asset category": "Demirbaş varlık kategorisi", "Fixture asset class": "Demirbaş varlık sınıfı", "Fixture model": "Demirbaş modeli", "Forecast type label": "<PERSON><PERSON><PERSON>", "Forecast type value": "<PERSON><PERSON><PERSON><PERSON>", "Type label": "<PERSON><PERSON><PERSON>", "Group value": "Grup değeri", "Group label": "Grup etiketi", "Document collection": "Belge koleksiyonu", "Document view": "Belge görünümü", "Status label": "<PERSON><PERSON> etiketi", "Partner type value": "İş ortağı türü değeri", "Partner type label": "İş ortağı türü etiketi", "Transaction type value": "İşlem türü değeri", "Transaction type label": "İşlem türü etiketi", "Currency name": "Para birimi adı", "Scope label": "<PERSON><PERSON><PERSON>", "Plus installment count": "Artı taksit sayısı", "Is government office": "Devlet dairesi mi?", "Birth place": "<PERSON><PERSON><PERSON> yeri", "Birth country": "<PERSON><PERSON><PERSON>", "Invoice status value": "<PERSON><PERSON>", "Vendor email": "Tedarikçi e-posta", "Vendor phone": "Tedarikçi telefon", "Vendor tin identity": "Tedarikçi tc/vergi no", "Is base list price": "<PERSON>mel liste fiyatı mı?", "Is gross price list": "<PERSON><PERSON><PERSON><PERSON> fiyat listesi mi?", "Is vendor list price": "Tedarikçi liste fiyatı mı?", "List price status": "Liste fiyatı durumu", "List price status value": "Liste fiyatı durumu değeri", "Reference partner code": "Referans iş ortağı kodu", "Reference partner name": "Referans iş ortağı adı", "Order status": "Sipariş durumu", "Can refresh the data": "Verileri yeni<PERSON>ilir", "Deleting": "Silini<PERSON><PERSON>", "Refreshing": "Yenileniyor", "Refresh All Data": "<PERSON><PERSON>m <PERSON>arı Yenile", "Dynamic": "Dinamik", "Fixed": "Sabit", "Last Purchase Price": "<PERSON>"}