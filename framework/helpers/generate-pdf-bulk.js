import puppeteer from 'puppeteer';

let browserInstance;

export async function initializeBrowser() {
    if (!browserInstance) {
        browserInstance = await puppeteer.launch({
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ],
            handleSIGINT: true,
            handleSIGTERM: true,
            handleSIGHUP: true,
            headless: 'new',
            devtools: false,
            executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
        });
    }
    return browserInstance;
}

export async function generatePDFBulk(content = '', options = {}) {
    if (!browserInstance) {
        await initializeBrowser();
    }

    const page = await browserInstance.newPage();

    try {
        await page.emulateMediaType('print');
        await page.setContent(content, {waitUntil: 'networkidle2'});

        const pdf = await page.pdf({
            displayHeaderFooter: true,
            footerTemplate:
                '<div style="text-align: right;width: 297mm;font-size: 8px;"><span style="margin-right: 1cm"><span class="pageNumber"></span> / <span class="totalPages"></span></span></div>',
            ...options
        });

        return pdf;
    } finally {
        await page.close();
    }
}

export async function closeBrowser() {
    if (browserInstance) {
        await browserInstance.close();
        browserInstance = null;
    }
}
